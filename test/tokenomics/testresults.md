npm run test:tokenomics

> WarpSector-game@1.0.0 test:tokenomics
> node test/tokenomics/runStressTest.js

🚀 Starting WarpSector Tokenomics Stress Test
============================================================
📋 Test Configuration:
   Duration: 300 seconds
   Max Users: 10
   API URL: http://localhost:3001/api
   Hardhat URL: http://localhost:8545
   Hot Wallet: ******************************************
   Scenario: All scenarios
   Verbose: Disabled

🔍 Validating prerequisites...
   ✅ Game Server: OK
   ✅ Hardhat Node: OK
   ✅ Hot Wallet Balance: OK
   ✅ Test Accounts: OK
✅ All prerequisites validated

🧪 TokenomicsStressTest initialized
📊 Configuration: {
  apiBaseUrl: 'http://localhost:3001/api',
  hardhatUrl: 'http://localhost:8545',
  chainId: 31337,
  hotWalletAddress: '******************************************',
  testDuration: 300000,
  maxConcurrentUsers: 10,
  verbose: false,
  reportFile: null,
  scenario: null
}
🚀 Initializing Tokenomics Stress Test Framework...
✅ Server health check passed: { status: 'OK', message: 'AI Service Server is running' }
🔗 Connected to network: Network {}
💰 Hot wallet balance: 100000.0 ETH
👥 Found 20 test accounts
📊 TransactionTracker initialized
💰 TreasuryMonitor initialized
📊 Monitoring systems initialized
✅ Initialized daily reward tracker for user: ******************************************
👤 Created grinder user simulator: grinder_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created grinder user simulator: grinder_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created grinder user simulator: grinder_3 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created whale user simulator: whale_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created whale user simulator: whale_2 (******************************************)
✅ Initialized daily reward tracker for user: 0x9965507d1a55bcc2695c58ba16fb37d819b0a4dc
👤 Created creator user simulator: creator_1 (0x9965507d1a55bcc2695c58ba16fb37d819b0a4dc)
✅ Initialized daily reward tracker for user: 0x976ea74026e726554db657fa54763abd0c3a0aa9
👤 Created creator user simulator: creator_2 (0x976ea74026e726554db657fa54763abd0c3a0aa9)
✅ Initialized daily reward tracker for user: 0x14dc79964da2c08b23698b3d3cc7ca32193d9955
👤 Created casual user simulator: casual_1 (0x14dc79964da2c08b23698b3d3cc7ca32193d9955)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created casual user simulator: casual_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created casual user simulator: casual_3 (******************************************)
👥 Created 10 user simulators
✅ Stress test framework initialized successfully
🚀 Starting Tokenomics Stress Test...
💰 Initial treasury balance: 100000 ETH
🚀 TreasuryMonitor started
🚀 TransactionTracker started
📊 Monitoring started
🎮 Running Sequential Grinding Test (Attack Vector 1)...
📋 Objective: Test if grinder earnings can drain treasury
👥 Testing with 3 grinder accounts
🎨 Running Creator Reward Test (Attack Vector 2)...
📋 Objective: Test creator reward sustainability and 50% distribution accuracy
👨‍🎨 Testing with 2 creators and 2 whales
🎨 Phase 1: Environment Creation
🎨 creator_1 creating environment: "Ancient temple ruins in a jungle"
👥 Running Multi-Account Coordination Test (Attack Vector 3)...
📋 Objective: Test economic balance under coordinated behavior
🤝 Simulating coordinated behavior across 5 accounts
⚡ Starting coordinated actions...
💰 Running Treasury Drain Test (Maximum Stress)...
📋 Objective: Test maximum stress on treasury with all users acting simultaneously
🤝 grinder_1 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_1...
🔐 Authenticated grinder_1 with server
🎮 Level 1: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 1 completion - 100% enemies defeated
💰 Initial treasury balance: 100000 ETH
🎯 Starting grinder session: grinder_1
🚫 Level 1: No reward (level_already_completed_today)
💰 Pre-stress treasury balance: 100000 ETH
⚡ Initiating maximum stress scenario...
⚡ whale_2 starting maximum stress behavior
🛒 Reality Warp: 25000 WISH → 2500 WISH (90% off) → 25 ETH
💸 whale_2 spending 25 ETH for: Reality Warp
💸 Token Spend Tracked: 25 tokens from whale_2
💸 whale_2 completed spend transaction for: Reality Warp
⚡ grinder_2 starting maximum stress behavior
🎮 Level 1: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 1 completion - 100% enemies defeated
⚡ creator_1 starting maximum stress behavior
🎨 creator_1 creating environment: "Underwater coral reef with bioluminescence"
⚡ grinder_1 starting maximum stress behavior
🚫 Level 1: No reward (level_already_completed_today)
⚡ casual_3 starting maximum stress behavior
🤝 grinder_2 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_2...
🔐 Authenticated grinder_2 with server
🚫 Level 1: No reward (level_already_completed_today)
⚡ casual_1 starting maximum stress behavior
🛒 Extra Life: 15000 WISH → 1500 WISH (90% off) → 15 ETH
💸 whale_2 spending 15 ETH for: Extra Life
💸 Token Spend Tracked: 15 tokens from whale_2
💸 whale_2 completed spend transaction for: Extra Life
✅ API Call: POST /wallet/send (306ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x639eed21262c1176b6548058565b81403133cdf1f1194876577e48edf94a3517
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_1: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 1.25 ETH, 0x639eed21262c1176b6548058565b81403133cdf1f1194876577e48edf94a3517
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_1
⚡ casual_2 starting maximum stress behavior
✅ API Call: POST /wallet/send (187ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0xe2ea14c7ba3e565876885b94f198b51296a29b0cb3d2a6273cca786828dcb422
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_2: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 1.25 ETH, 0xe2ea14c7ba3e565876885b94f198b51296a29b0cb3d2a6273cca786828dcb422
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_2
⚡ creator_2 starting maximum stress behavior
🎨 creator_2 creating environment: "Underwater coral reef with bioluminescence"
🤝 grinder_3 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_3...
🔐 Authenticated grinder_3 with server
🎮 Level 1: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 1 completion - 100% enemies defeated
⚡ whale_1 starting maximum stress behavior
🛒 Reality Warp: 25000 WISH → 2500 WISH (90% off) → 25 ETH
💸 whale_1 spending 25 ETH for: Reality Warp
💸 Token Spend Tracked: 25 tokens from whale_1
💸 whale_1 completed spend transaction for: Reality Warp
🛒 Extra Wingman: 10000 WISH → 1000 WISH (90% off) → 10 ETH
💸 whale_2 spending 10 ETH for: Extra Wingman
💸 Token Spend Tracked: 10 tokens from whale_2
💸 whale_2 completed spend transaction for: Extra Wingman
🎮 Level 2: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 2 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (179ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0xe232917bf6d41f01159a140457bf712d06fd20f1db288b5996fc5b0f0cfb5517
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_3: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 1.25 ETH, 0xe232917bf6d41f01159a140457bf712d06fd20f1db288b5996fc5b0f0cfb5517
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_3
⚡ grinder_3 starting maximum stress behavior
🚫 Level 1: No reward (level_already_completed_today)
🤝 whale_1 starting coordinated behavior simulation
🎮 Starting whale session for whale_1...
🔐 Authenticated whale_1 with server
🛒 Reality Warp: 25000 WISH → 2500 WISH (90% off) → 25 ETH
💸 whale_1 spending 25 ETH for: Reality Warp
💸 Token Spend Tracked: 25 tokens from whale_1
💸 whale_1 completed spend transaction for: Reality Warp
🛒 Extra Life: 15000 WISH → 1500 WISH (90% off) → 15 ETH
💸 whale_1 spending 15 ETH for: Extra Life
💸 Token Spend Tracked: 15 tokens from whale_1
💸 whale_1 completed spend transaction for: Extra Life
🚫 Level 2: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (163ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0xb40b5ac98085574a04921bf539509e1ae4e0196441380157a187aa00f36fa878
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_1: Level 2 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 1.25 ETH, 0xb40b5ac98085574a04921bf539509e1ae4e0196441380157a187aa00f36fa878
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_1
🛒 Spread Ammo: 7500 WISH → 750 WISH (90% off) → 7.5 ETH
💸 whale_2 spending 7.5 ETH for: Spread Ammo
💸 Token Spend Tracked: 7.5 tokens from whale_2
💸 whale_2 completed spend transaction for: Spread Ammo
🎮 Level 2: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 2 completion - 100% enemies defeated
🤝 whale_2 starting coordinated behavior simulation
🎮 Starting whale session for whale_2...
🔐 Authenticated whale_2 with server
🛒 Reality Warp: 25000 WISH → 2500 WISH (90% off) → 25 ETH
💸 whale_2 spending 25 ETH for: Reality Warp
💸 Token Spend Tracked: 25 tokens from whale_2
💸 whale_2 completed spend transaction for: Reality Warp
🛒 Extra Life: 15000 WISH → 1500 WISH (90% off) → 15 ETH
💸 whale_1 spending 15 ETH for: Extra Life
💸 Token Spend Tracked: 15 tokens from whale_1
💸 whale_1 completed spend transaction for: Extra Life
🚫 Level 2: No reward (level_already_completed_today)
🚫 Level 2: No reward (level_already_completed_today)
🛒 Extra Wingman: 10000 WISH → 1000 WISH (90% off) → 10 ETH
💸 whale_1 spending 10 ETH for: Extra Wingman
💸 Token Spend Tracked: 10 tokens from whale_1
💸 whale_1 completed spend transaction for: Extra Wingman
✅ API Call: POST /wallet/send (174ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x631803bc0b410c430876cf3c58b5df60635c749299320a762d95c37ed0ebb16b
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_2: Level 2 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 1.25 ETH, 0x631803bc0b410c430876cf3c58b5df60635c749299320a762d95c37ed0ebb16b
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_2
🛒 Extra Life: 15000 WISH → 1500 WISH (90% off) → 15 ETH
💸 whale_2 spending 15 ETH for: Extra Life
💸 Token Spend Tracked: 15 tokens from whale_2
💸 whale_2 completed spend transaction for: Extra Life
🛒 Extra Wingman: 10000 WISH → 1000 WISH (90% off) → 10 ETH
💸 whale_1 spending 10 ETH for: Extra Wingman
💸 Token Spend Tracked: 10 tokens from whale_1
💸 whale_1 completed spend transaction for: Extra Wingman
🛒 Spread Ammo: 7500 WISH → 750 WISH (90% off) → 7.5 ETH
💸 whale_1 spending 7.5 ETH for: Spread Ammo
💸 Token Spend Tracked: 7.5 tokens from whale_1
💸 whale_1 completed spend transaction for: Spread Ammo
🎮 Level 2: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 2 completion - 100% enemies defeated
🚫 Level 2: No reward (level_already_completed_today)
🎮 Level 3: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 3 completion - 100% enemies defeated
🚫 Level 3: No reward (level_already_completed_today)
🛒 Extra Wingman: 10000 WISH → 1000 WISH (90% off) → 10 ETH
💸 whale_2 spending 10 ETH for: Extra Wingman
💸 Token Spend Tracked: 10 tokens from whale_2
💸 whale_2 completed spend transaction for: Extra Wingman
🛒 Spread Ammo: 7500 WISH → 750 WISH (90% off) → 7.5 ETH
💸 whale_1 spending 7.5 ETH for: Spread Ammo
💸 Token Spend Tracked: 7.5 tokens from whale_1
💸 whale_1 completed spend transaction for: Spread Ammo
🚫 Level 3: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (244ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x7b7836234f14e23e06ce2fffb403e17632a49100eaf983edc60a15116ccbd063
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_3: Level 2 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 1.25 ETH, 0x7b7836234f14e23e06ce2fffb403e17632a49100eaf983edc60a15116ccbd063
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_3
🎮 Level 3: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 3 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (189ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x23367053d1e403fc8b363a45dc9f19e18f73f71e5de7563d7106e5b90b49cf1f
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_1: Level 3 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 1.25 ETH, 0x23367053d1e403fc8b363a45dc9f19e18f73f71e5de7563d7106e5b90b49cf1f
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_1
🚫 Level 3: No reward (level_already_completed_today)
🛒 Spread Ammo: 7500 WISH → 750 WISH (90% off) → 7.5 ETH
💸 whale_2 spending 7.5 ETH for: Spread Ammo
💸 Token Spend Tracked: 7.5 tokens from whale_2
💸 whale_2 completed spend transaction for: Spread Ammo
🎁 Awarding 375 ETH to whale_1 for: Level 1 completion - minimal effort
✅ API Call: POST /wallet/send (141ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x11e1fdf12ecb13531e81db13627fcd81164fa848bd34111ac1a4f16b94875b45
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_2: Level 3 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 1.25 ETH, 0x11e1fdf12ecb13531e81db13627fcd81164fa848bd34111ac1a4f16b94875b45
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_2
✅ API Call: POST /wallet/send (178ms)
💰 ETH Transfer Tracked: 375 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 375 ETH to ******************************************
📤 Transaction hash: 0xaf2bfe11d21a8e510e06ae8e1718b4a1296b56d35a97fb91dfa4d379fb30f27e
📉 Treasury outflow recorded: -375.000000 ETH (Reward to whale_1: Level 1 completion - minimal effort)
🔍 Tracking token award: whale_1, 375 ETH, 0xaf2bfe11d21a8e510e06ae8e1718b4a1296b56d35a97fb91dfa4d379fb30f27e
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=whale_1, amount=375
🎁 Token Award Tracked: 375 tokens to whale_1
🎮 Level 3: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 3 completion - 100% enemies defeated
🎁 Awarding 375 ETH to whale_2 for: Level 1 completion - minimal effort
🎮 Level 4: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 4 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (154ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x882ca904526364ac829aa98566d352eda86227f36631b2ca47e828c948a7f714
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_3: Level 3 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 1.25 ETH, 0x882ca904526364ac829aa98566d352eda86227f36631b2ca47e828c948a7f714
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_3
✅ API Call: POST /wallet/send (156ms)
💰 ETH Transfer Tracked: 375 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 375 ETH to ******************************************
📤 Transaction hash: 0xb81d434c622d8463ec61216f0d18b2af25b0142407735ffaa2394f35c75085e4
📉 Treasury outflow recorded: -375.000000 ETH (Reward to whale_2: Level 1 completion - minimal effort)
🔍 Tracking token award: whale_2, 375 ETH, 0xb81d434c622d8463ec61216f0d18b2af25b0142407735ffaa2394f35c75085e4
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=whale_2, amount=375
🎁 Token Award Tracked: 375 tokens to whale_2
🚫 Level 4: No reward (level_already_completed_today)
🚫 Level 3: No reward (level_already_completed_today)
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (191ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x1a8403aa8cf763b0da90a6cb82297d5cbdb61eef5cbfb7a270b4c0739e7876de
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_1: Level 4 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 1.25 ETH, 0x1a8403aa8cf763b0da90a6cb82297d5cbdb61eef5cbfb7a270b4c0739e7876de
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_1
🎮 Level 4: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 4 completion - 100% enemies defeated
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (138ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0xae28896c6a4ac648115b49d60c83130783d6ff653cdda9d584038e1e540c578f
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_2: Level 4 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 1.25 ETH, 0xae28896c6a4ac648115b49d60c83130783d6ff653cdda9d584038e1e540c578f
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_2
🎮 Level 4: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 4 completion - 100% enemies defeated
🎮 Level 5: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 5 completion - 100% enemies defeated
🚫 Level 4: No reward (level_already_completed_today)
🚫 Level 5: No reward (level_already_completed_today)
🚫 Level 5: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (223ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0xdd44aead2556470875f42e8144a177651f7d4f1bcc7d2435991a7ae195113013
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_3: Level 4 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 1.25 ETH, 0xdd44aead2556470875f42e8144a177651f7d4f1bcc7d2435991a7ae195113013
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_3
🎮 Level 5: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 5 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (193ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0xc2d0a956076172a8f5c36ab307e501db85ee990f2a17d775a4631d166e060046
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_1: Level 5 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 1.25 ETH, 0xc2d0a956076172a8f5c36ab307e501db85ee990f2a17d775a4631d166e060046
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_1
🚫 Level 5: No reward (level_already_completed_today)
🎁 Awarding 750 ETH to whale_1 for: Level 2 completion - minimal effort
✅ API Call: POST /wallet/send (148ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x545c7325d06e9b13f10dffb6f7f61ac6908ecc87ba3749e0060bcdd5b67144ec
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_2: Level 5 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 1.25 ETH, 0x545c7325d06e9b13f10dffb6f7f61ac6908ecc87ba3749e0060bcdd5b67144ec
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_2
🎁 Awarding 750 ETH to whale_2 for: Level 2 completion - minimal effort
✅ API Call: POST /wallet/send (190ms)
💰 ETH Transfer Tracked: 750 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 750 ETH to ******************************************
📤 Transaction hash: 0x88ae34ef3d960b8c0cc4789a3ff71a2ba27136d23cce786fc5dfcd04c092f3c9
📉 Treasury outflow recorded: -750.000000 ETH (Reward to whale_1: Level 2 completion - minimal effort)
🔍 Tracking token award: whale_1, 750 ETH, 0x88ae34ef3d960b8c0cc4789a3ff71a2ba27136d23cce786fc5dfcd04c092f3c9
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=whale_1, amount=750
🎁 Token Award Tracked: 750 tokens to whale_1
🎮 Level 5: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 5 completion - 100% enemies defeated
🎮 Level 6: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 6 completion - 100% enemies defeated
🚫 Level 6: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (140ms)
💰 ETH Transfer Tracked: 750 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 750 ETH to ******************************************
📤 Transaction hash: 0xc90bc5dd2e6f40d4d4a8615e39677e17153369b2a868890ea8f2581a5e13471c
📉 Treasury outflow recorded: -750.000000 ETH (Reward to whale_2: Level 2 completion - minimal effort)
🔍 Tracking token award: whale_2, 750 ETH, 0xc90bc5dd2e6f40d4d4a8615e39677e17153369b2a868890ea8f2581a5e13471c
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=whale_2, amount=750
🎁 Token Award Tracked: 750 tokens to whale_2
✅ API Call: POST /wallet/send (146ms)
💰 ETH Transfer Tracked: 1.25 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x95f7be1c99a3bdf250102022470fbf995ba6b91f9ef37411cae6c289d85a77d6
📉 Treasury outflow recorded: -1.250000 ETH (Reward to grinder_3: Level 5 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 1.25 ETH, 0x95f7be1c99a3bdf250102022470fbf995ba6b91f9ef37411cae6c289d85a77d6
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=1.25
🎁 Token Award Tracked: 1.25 tokens to grinder_3
🚫 Level 5: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (159ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x45fcc6f0f1525c64dfc0391121a92c1323757cd755a576acec5a98d9f13c1104
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_1: Level 6 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 2 ETH, 0x45fcc6f0f1525c64dfc0391121a92c1323757cd755a576acec5a98d9f13c1104
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_1
🚫 Level 6: No reward (level_already_completed_today)
🎮 Level 6: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 6 completion - 100% enemies defeated
🚫 Level 6: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (143ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xf90eeabf272f0248073863b2c07dcdf4bdb1c8f68fc6df3eac312c417337ae77
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_2: Level 6 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 2 ETH, 0xf90eeabf272f0248073863b2c07dcdf4bdb1c8f68fc6df3eac312c417337ae77
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_2
🎮 Level 7: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 7 completion - 100% enemies defeated
🎮 Level 6: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 6 completion - 100% enemies defeated
🚫 Level 6: No reward (level_already_completed_today)
🚫 Level 7: No reward (level_already_completed_today)
🚫 Level 7: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (155ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xd4e67ccdc44f03521eb718eaf0b3216afcd52eebb0daf31e8eb8242463287c46
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_1: Level 7 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 2 ETH, 0xd4e67ccdc44f03521eb718eaf0b3216afcd52eebb0daf31e8eb8242463287c46
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_1
✅ API Call: POST /wallet/send (147ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xc65065fe98889a5bebaa876d72b4ff8bb32a21a05b5adf9729962054ac67cf3c
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_3: Level 6 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 2 ETH, 0xc65065fe98889a5bebaa876d72b4ff8bb32a21a05b5adf9729962054ac67cf3c
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_3
🎮 Level 7: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 7 completion - 100% enemies defeated
🚫 Level 7: No reward (level_already_completed_today)
💰 Whale whale_1 completed session with heavy spending
✅ Completed whale session for whale_1
✅ API Call: POST /wallet/send (157ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xf7cefdbc3a9f39cf59c760277116b6800d450edab29586701b80a3c0174efae4
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_2: Level 7 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 2 ETH, 0xf7cefdbc3a9f39cf59c760277116b6800d450edab29586701b80a3c0174efae4
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_2
💰 Whale whale_2 completed session with heavy spending
✅ Completed whale session for whale_2
🎮 Level 7: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 7 completion - 100% enemies defeated
🎮 Level 8: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 8 completion - 100% enemies defeated
🚫 Level 8: No reward (level_already_completed_today)
🚫 Level 8: No reward (level_already_completed_today)
🚫 Level 7: No reward (level_already_completed_today)
🎮 Level 8: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 8 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (186ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xedc6e8a844de500cafabe4c60a397661d2cb30c46d7d5c6eb9db518642583461
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_3: Level 7 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 2 ETH, 0xedc6e8a844de500cafabe4c60a397661d2cb30c46d7d5c6eb9db518642583461
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_3
✅ API Call: POST /wallet/send (160ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x849b21889f3e8ad9e0020d3a88e469e9a5fdcb82939f2119392066afc6280787
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_1: Level 8 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 2 ETH, 0x849b21889f3e8ad9e0020d3a88e469e9a5fdcb82939f2119392066afc6280787
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_1
🚫 Level 8: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (136ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x591ad5640c61558e6ee54e4c4735ef444df0988a354212ea27253848b3a92455
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_2: Level 8 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 2 ETH, 0x591ad5640c61558e6ee54e4c4735ef444df0988a354212ea27253848b3a92455
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_2
🎮 Level 9: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 9 completion - 100% enemies defeated
🚫 Level 9: No reward (level_already_completed_today)
🎮 Level 8: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 8 completion - 100% enemies defeated
🚫 Level 8: No reward (level_already_completed_today)
🚫 Level 9: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (186ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x3fe0d48d25877c736c3bdfbc3b16a945bad417febec9aa982064a5427db1c828
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_1: Level 9 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 2 ETH, 0x3fe0d48d25877c736c3bdfbc3b16a945bad417febec9aa982064a5427db1c828
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_1
🎮 Level 9: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 9 completion - 100% enemies defeated
🚫 Level 9: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (178ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xc7800c3f1cff38c916662129304520dbf676494112dd8d29a58d3dba30367c48
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_3: Level 8 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 2 ETH, 0xc7800c3f1cff38c916662129304520dbf676494112dd8d29a58d3dba30367c48
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_3
✅ API Call: POST /wallet/send (169ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xe047cecf7e1ca3b5a8bb3d9b69332d876bef3f7d1d2be7f0892d711276649f99
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_2: Level 9 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 2 ETH, 0xe047cecf7e1ca3b5a8bb3d9b69332d876bef3f7d1d2be7f0892d711276649f99
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_2
🎮 Level 10: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 10 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (129ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x497be8e2463ed8f40ebfb8b015f8b832e341c65f64bd6c43b6460326fbc8734f
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_1: Level 10 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 2 ETH, 0x497be8e2463ed8f40ebfb8b015f8b832e341c65f64bd6c43b6460326fbc8734f
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_1
🎮 Level 9: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 9 completion - 100% enemies defeated
🚫 Level 10: No reward (level_already_completed_today)
🚫 Level 10: No reward (level_already_completed_today)
🎮 Level 10: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 10 completion - 100% enemies defeated
🚫 Level 9: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (147ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x1c78b2fa7e84610849c8684aa344c7ca816c54d4352b96f880293d3290dda38d
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_3: Level 9 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 2 ETH, 0x1c78b2fa7e84610849c8684aa344c7ca816c54d4352b96f880293d3290dda38d
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_3
✅ API Call: POST /wallet/send (126ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x214ab87a029fe61c3a37adbbd1de78819b502c884914d38dd3157f82e61c05b0
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_2: Level 10 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 2 ETH, 0x214ab87a029fe61c3a37adbbd1de78819b502c884914d38dd3157f82e61c05b0
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_2
🚫 Level 10: No reward (level_already_completed_today)
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_1: 2296.750693 ETH
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_1
🎮 Level 10: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 10 completion - 100% enemies defeated
🚫 Level 10: No reward (level_already_completed_today)
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_2
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
✅ API Call: POST /wallet/send (157ms)
💰 ETH Transfer Tracked: 2 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xf59b510e0d61754bccc150d0f3251d00f50d0079067c95003b2c2f1dbe7f81b2
📉 Treasury outflow recorded: -2.000000 ETH (Reward to grinder_3: Level 10 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 2 ETH, 0xf59b510e0d61754bccc150d0f3251d00f50d0079067c95003b2c2f1dbe7f81b2
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=2
🎁 Token Award Tracked: 2 tokens to grinder_3
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_3
🎯 Starting grinder session: grinder_2
🚫 Level 1: No reward (level_already_completed_today)
🚫 Level 2: No reward (level_already_completed_today)
📊 Multi-Account Coordination Results:
   Coordination Duration: 7347ms
   Accounts Coordinated: 5
   Treasury Impact: Analyzing...
🚫 Level 3: No reward (level_already_completed_today)
🚫 Level 4: No reward (level_already_completed_today)
🚫 Level 5: No reward (level_already_completed_today)
🚫 Level 6: No reward (level_already_completed_today)
🚫 Level 7: No reward (level_already_completed_today)
🚫 Level 8: No reward (level_already_completed_today)
🚫 Level 9: No reward (level_already_completed_today)
🚫 Level 10: No reward (level_already_completed_today)
✅ API Call: POST /generate-environment (11690ms)
🎨 Environment Creation Tracked: Temple of the Ancient Ones by creator_1
✅ Environment created by creator_1: Temple of the Ancient Ones
✅ Environment created: Temple of the Ancient Ones by creator_1
✅ Environment created: Temple of the Ancient Ones by creator_1
🎨 creator_2 creating environment: "Volcanic landscape with lava flows"
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_2: 2298.750714 ETH
✅ API Call: POST /generate-environment (11981ms)
🎨 Environment Creation Tracked: Bioluminescent Reef by creator_1
✅ Environment created by creator_1: Bioluminescent Reef
✅ Environment created: Bioluminescent Reef by creator_1
🎯 Starting grinder session: grinder_3
🚫 Level 1: No reward (level_already_completed_today)
🚫 Level 2: No reward (level_already_completed_today)
🚫 Level 3: No reward (level_already_completed_today)
🚫 Level 4: No reward (level_already_completed_today)
🚫 Level 5: No reward (level_already_completed_today)
🚫 Level 6: No reward (level_already_completed_today)
🚫 Level 7: No reward (level_already_completed_today)
✅ API Call: POST /generate-environment (15484ms)
🎨 Environment Creation Tracked: Underwater Coral Reef by creator_2
✅ Environment created by creator_2: Underwater Coral Reef
✅ Environment created: Underwater Coral Reef by creator_2
📊 Treasury Drain Test Results:
   Stress Duration: 15797ms
   Users Participating: 10
   Initial Balance: 100000 ETH
   Final Balance: 97701.24928599999 ETH
   Total Drain: 2298.750714 ETH
   Drain Percentage: 2.30%
   Treasury Survived: ✅ YES
🚫 Level 8: No reward (level_already_completed_today)
🚫 Level 9: No reward (level_already_completed_today)
🚫 Level 10: No reward (level_already_completed_today)
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_3: 2298.750714 ETH
📈 Sequential Grinding Test Results:
   Initial Balance: 100000 ETH
   Final Balance: 97701.24928599999 ETH
   Total Impact: 2298.750714 ETH
   Treasury Sustainable: ✅ YES
✅ API Call: POST /generate-environment (7322ms)
🎨 Environment Creation Tracked: Lava Wastes by creator_2
✅ Environment created by creator_2: Lava Wastes
✅ Environment created: Lava Wastes by creator_2
✅ Environment created: Lava Wastes by creator_2
💰 Phase 2: Mystical Environment Purchases
🐋 whale_1 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens
💰 whale_1 purchasing Mystical Environment for 25000 tokens
💸 whale_1 spending 25000 ETH for: Mystical Environment Purchase
💸 Token Spend Tracked: 25000 tokens from whale_1
🎨 Mystical Environment purchase detected - distributing 12500 ETH to creator
✅ API Call: POST /rewards/distribute (179ms)
💰 ETH Transfer Tracked: 12500 ETH from ****************************************** to ******************************************
✅ REAL creator reward distributed: 12500 ETH
📤 Reward transaction hash: 0xe4fb8e02aee80356edaa10935caafb9a5f7df15e57f98823dad92ae1a58bbdc0
💸 whale_1 completed spend transaction for: Mystical Environment Purchase
🎁 Mystical Environment purchase should trigger 50% creator reward
🐋 whale_2 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens
💰 whale_2 purchasing Mystical Environment for 25000 tokens
💸 whale_2 spending 25000 ETH for: Mystical Environment Purchase
💸 Token Spend Tracked: 25000 tokens from whale_2
🎨 Mystical Environment purchase detected - distributing 12500 ETH to creator
✅ API Call: POST /rewards/distribute (159ms)
💰 ETH Transfer Tracked: 12500 ETH from ****************************************** to ******************************************
✅ REAL creator reward distributed: 12500 ETH
📤 Reward transaction hash: 0x63300d5fc309d067d071d689c904a0aab75402109fa601d9a461ac7cd39a8880
💸 whale_2 completed spend transaction for: Mystical Environment Purchase
🎁 Mystical Environment purchase should trigger 50% creator reward
💰 Balance change: -25000.000042 ETH (72701.249244 ETH total)
📉 Treasury outflow detected: -25000.000042 ETH
📊 Creator Reward Test Results:
   Total Purchases: 50000 tokens
   Expected Creator Rewards: 25000 tokens (50%)
   Environments Created: 2
   Creator Reward Accuracy: Testing 50% distribution...
🛑 TreasuryMonitor stopped
📊 Monitored for 20360ms
💰 Final balance: 72701.24924399999 ETH
📈 Balance change: -27298.750756 ETH
🛑 TransactionTracker stopped
📊 Tracked 92 transactions in 20360ms
📊 Monitoring stopped
🔍 Running comprehensive validation...
🔍 Starting comprehensive validation...
💰 Validating treasury sustainability...
🔍 Treasury Status Debug:
🎨 Validating creator reward distribution...
🔍 Creator Analysis Debug:
🎮 Validating grinder behavior...
🔍 User Activity Debug:
🔗 Validating transaction integrity...
🔍 Transaction Data Debug:
⚖️ Validating economic balance...
🔧 Validating system stability...

🔍 VALIDATION RESULTS
==================================================
Overall Result: ❌ FAIL
Tests Passed: 20/25 (80.0%)
Critical Issues: 4
Warnings: 1

❌ CRITICAL ISSUES:
   1. Creator Reward Accuracy (50%): 100% (expected: 45-55%)
   2. Environment Purchases Trigger Rewards: 0.0% (expected: ≥ 80%)
   3. Treasury Burn Rate Reasonable: 1363.710199 ETH/s (expected: ≤ 0.01 ETH/s)
   4. Projected Treasury Runtime: 0 minutes (expected: > 60 minutes)

⚠️ WARNINGS:
   1. Treasury Risk Level Acceptable: high (expected: not critical)

💡 RECOMMENDATIONS:
   1. CRITICAL: Address all critical issues before production deployment
   2. Fix creator reward distribution logic to ensure 50% accuracy
   3. Review warning items for potential improvements

✅ Validation completed
📊 Test Report Generated
✅ Stress test completed

📊 STRESS TEST RESULTS
============================================================
📋 Test Summary:
   Total Duration: 20.386 seconds
   Users Simulated: 10
   Total Transactions: 92
   Test Completion: Early completion

💰 Treasury Analysis:
   Initial Balance: 100000 ETH
   Final Balance: 72701.24924399999 ETH
   Balance Change: -27298.75075600001 ETH
   Net Flow: -27298.750042 ETH
   Risk Level: high

🎨 Creator Reward Analysis:
   Total Creator Rewards: 25000 ETH
   Average Reward per Environment: 12500.0000 ETH
   Reward Distribution Accuracy: 100%

📈 Sustainability Assessment:
   Is Sustainable: ✅ YES
   Risk Level: high
   Projected Runtime: undefined seconds

💡 Recommendations:
   1. HIGH RISK: Treasury balance declining rapidly
   2. Monitor closely and consider adjusting tokenomics parameters
   3. Net outflow detected - more rewards being paid than purchases received

✅ Stress test completed successfully